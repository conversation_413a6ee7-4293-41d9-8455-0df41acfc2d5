C:\AlgoTrading\venv\Lib\site-packages\pandas_ta\__init__.py:8: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  from pkg_resources import get_distribution, DistributionNotFound
Traceback (most recent call last):
  File "C:\AlgoTrading\run_backtest.py", line 28, in <module>
    from src.trading.fyers_client import FyersClient
  File "C:\AlgoTrading\src\trading\fyers_client.py", line 9, in <module>
    from src.auth import fyers_auth
  File "C:\AlgoTrading\src\auth\fyers_auth.py", line 52, in <module>
    payload2 = {"request_key": res2["request_key"], "identity_type": "pin", "identifier": getEncodedString(fyers_pin)}
                               ~~~~^^^^^^^^^^^^^^^
KeyError: 'request_key'
