C:\AlgoTrading\venv\Lib\site-packages\pandas_ta\__init__.py:8: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  from pkg_resources import get_distribution, DistributionNotFound
2025-07-30 01:24:32,638 - INFO - Initializing Fyers API client...
2025-07-30 01:24:32,638 - INFO - Fyers API client initialized successfully
2025-07-30 01:24:32,638 - INFO - \U0001f680 Starting Nifty backtest with Fyers API data...
2025-07-30 01:24:32,638 - INFO - Symbol: NSE:NIFTY50-INDEX
2025-07-30 01:24:32,638 - INFO - Timeframe: 5 minutes
2025-07-30 01:24:32,638 - INFO - Lookback: 30 days
2025-07-30 01:24:32,638 - INFO - Initial Capital: \u20b9100,000.00
2025-07-30 01:24:32,638 - INFO - Starting Nifty backtest with Fyers API data...
2025-07-30 01:24:32,638 - INFO - Fetching NSE:NIFTY50-INDEX data for past 30 days...
2025-07-30 01:24:32,638 - INFO - Timeframe: 5 minutes
2025-07-30 01:24:32,638 - INFO - Date range: 2025-06-30 to 2025-07-30
2025-07-30 01:24:33,524 - INFO - Successfully fetched 1725 data points
2025-07-30 01:24:33,524 - INFO - Data columns: ['open', 'high', 'low', 'close', 'volume']
2025-07-30 01:24:33,524 - INFO - Date range in data: 2025-06-30 03:45:00 to 2025-07-29 09:55:00
2025-07-30 01:24:33,524 - INFO - Processing raw data to generate features...
2025-07-30 01:24:33,524 - INFO - Processing in-memory DataFrame with 1725 rows...
2025-07-30 01:24:35,712 - INFO - Removed 199 rows with NaN values. Final dataset: 1526 rows
2025-07-30 01:24:35,712 - INFO - Generated 58 features for DataFrame
2025-07-30 01:24:35,712 - INFO - Data processed successfully!
2025-07-30 01:24:35,713 - INFO - Processed data shape: (1526, 63)
2025-07-30 01:24:35,713 - INFO - Loading universal model from: models\universal_final_model.pth
2025-07-30 01:24:38,262 - ERROR - Failed to load MoE model from models\universal_final_model.pth: Weights only load failed. This file can still be loaded, to do so you have two options, do those steps only if you trust the source of the checkpoint. 
	(1) In PyTorch 2.6, we changed the default value of the `weights_only` argument in `torch.load` from `False` to `True`. Re-running `torch.load` with `weights_only` set to `False` will likely succeed, but it can result in arbitrary code execution. Do it only if you got the file from a trusted source.
	(2) Alternatively, to load with `weights_only=True` please check the recommended steps in the following error message.
	WeightsUnpickler error: Unsupported global: GLOBAL numpy._core.multiarray.scalar was not an allowed global by default. Please use `torch.serialization.add_safe_globals([numpy._core.multiarray.scalar])` or the `torch.serialization.safe_globals([numpy._core.multiarray.scalar])` context manager to allowlist this global if you trust this class/function.

Check the documentation of torch.load to learn more about types accepted by default with weights_only https://pytorch.org/docs/stable/generated/torch.load.html.
2025-07-30 01:24:38,262 - INFO - Using fresh model initialization
2025-07-30 01:24:38,262 - INFO - Successfully loaded universal model!
2025-07-30 01:24:38,262 - INFO - Using processed data with 1526 rows and 63 columns
2025-07-30 01:24:38,387 - INFO - Loaded final data for Nifty: 1526 rows from Nifty.csv
2025-07-30 01:24:38,387 - INFO - \U0001f4ca Data Feeding Strategy Manager initialized
2025-07-30 01:24:38,387 - INFO -    Total data length: 1526
2025-07-30 01:24:38,387 - INFO -    Data segments: 0
2025-07-30 01:24:38,387 - INFO -    Market regimes identified: 0
2025-07-30 01:24:38,387 - INFO - \U0001f3af Data feeding strategy initialized: curriculum
2025-07-30 01:24:38,387 - INFO - Observation space set: 64 features per step, total dim: 1286
2025-07-30 01:24:38,387 - WARNING - No data segments available for curriculum episode, using random episode
2025-07-30 01:24:38,387 - INFO - Running backtest on 1526 data points
2025-07-30 01:24:38,387 - ERROR - Error running backtest with Fyers data: mat1 and mat2 shapes cannot be multiplied (1x1286 and 63x256)
2025-07-30 01:24:38,434 - ERROR - Full traceback: Traceback (most recent call last):
  File "C:\AlgoTrading\run_backtest.py", line 221, in run_backtest_with_fyers_data
    action = agent.select_action(observation)  # Inference mode
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\AlgoTrading\src\agents\moe_agent.py", line 137, in select_action
    expert_weights = self.gating_network(market_features).squeeze(0)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\AlgoTrading\venv\Lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\AlgoTrading\venv\Lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\AlgoTrading\src\agents\moe_agent.py", line 76, in forward
    x = self.fc1(market_features)
        ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\AlgoTrading\venv\Lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\AlgoTrading\venv\Lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\AlgoTrading\venv\Lib\site-packages\torch\nn\modules\linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
RuntimeError: mat1 and mat2 shapes cannot be multiplied (1x1286 and 63x256)

2025-07-30 01:24:38,434 - ERROR - \u274c Nifty backtest failed
Fyers Profile:
    s  code message                                               data
0  ok   200          {'fy_id': 'XM22383', 'name': 'MARSHAL TUDU', '...
MoEAgent __init__: observation_dim=63, action_dim_discrete=2, action_dim_continuous=1, hidden_dim=256, num_experts=4
